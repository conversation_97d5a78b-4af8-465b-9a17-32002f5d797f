import React, { useState } from 'react';
import { ArrowDownToLine, RefreshCw } from 'lucide-react';
import { FormField } from './FormField';
import { fetchOrganizationDashboards, fetchDashboardDetails } from '../services/api';
import { Button } from './Button';
import { Dashboard } from '../types/dashboard';
import { useToast } from '../hooks/useToast';

export const ExportDashboard: React.FC = () => {
  const [grantKey, setGrantKey] = useState('');
  const [organizationId, setOrganizationId] = useState('');
  const [dashboards, setDashboards] = useState<Dashboard[]>([]);
  const [selectedDashboardId, setSelectedDashboardId] = useState('');
  const [loading, setLoading] = useState<'idle' | 'fetchingDashboards' | 'exportingDashboard'>('idle');
  const { showToast } = useToast();

  const handleFetchDashboards = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!grantKey || !organizationId) {
      showToast('Please provide both Grant Key and Organization ID', 'error');
      return;
    }

    if (!navigator.onLine) {
      showToast('No internet connection. Please check your network and try again.', 'error');
      return;
    }

    setLoading('fetchingDashboards');
    try {
      const fetchedDashboards = await fetchOrganizationDashboards(grantKey, organizationId);
      setDashboards(fetchedDashboards);
      setSelectedDashboardId(fetchedDashboards.length > 0 ? fetchedDashboards[0].id : '');
      showToast(`Found ${fetchedDashboards.length} dashboards`, 'success');
    } catch (error: any) {
      console.error('Error fetching dashboards:', error);
      const errorMessage = error.message || 'Unknown error';
      showToast(`Failed to fetch dashboards: ${errorMessage}. Please check your credentials and try again.`, 'error');
    } finally {
      setLoading('idle');
    }
  };

  const handleExportDashboard = async () => {
    if (!selectedDashboardId) {
      showToast('Please select a dashboard to export', 'error');
      return;
    }

    if (!navigator.onLine) {
      showToast('No internet connection. Please check your network and try again.', 'error');
      return;
    }

    setLoading('exportingDashboard');
    try {
      const dashboardDetails = await fetchDashboardDetails(grantKey, selectedDashboardId);
      
      const exportData = {
        name: dashboardDetails.name,
        type: dashboardDetails.type,
        tiles: dashboardDetails.tiles.nodes,
        roles: dashboardDetails.roles.nodes,
        exportedAt: new Date().toISOString(),
        sourceOrganizationId: organizationId
      };
      
      const dataStr = JSON.stringify(exportData, null, 2);
      const dataBlob = new Blob([dataStr], { type: 'application/json' });
      const url = URL.createObjectURL(dataBlob);
      
      const link = document.createElement('a');
      link.href = url;
      link.download = `${dashboardDetails.name.replace(/\s+/g, '_')}_dashboard_export.json`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);
      
      showToast('Dashboard exported successfully!', 'success');
    } catch (error: any) {
      console.error('Error exporting dashboard:', error);
      const errorMessage = error.message || 'Unknown error';
      showToast(`Failed to export dashboard: ${errorMessage}. Please try again.`, 'error');
    } finally {
      setLoading('idle');
    }
  };

  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-xl font-semibold mb-4">Export Dashboard</h2>
        <p className="text-gray-600 mb-4">
          Export a dashboard from your JobTread organization to share or backup.
        </p>
      </div>
      
      <form onSubmit={handleFetchDashboards} className="space-y-4">
        <FormField
          label="Grant Key"
          type="password"
          value={grantKey}
          onChange={setGrantKey}
          placeholder="Enter your JobTread API Grant Key"
          required
          helpText="The API Grant Key for the source organization"
        />
        
        <FormField
          label="Organization ID"
          type="text"
          value={organizationId}
          onChange={setOrganizationId}
          placeholder="Enter your Organization ID"
          required
          helpText="The ID of the organization containing the dashboards"
        />
        
        <Button 
          type="submit"
          disabled={loading !== 'idle'}
          isLoading={loading === 'fetchingDashboards'}
          icon={<RefreshCw className="h-4 w-4" />}
        >
          Fetch Dashboards
        </Button>
      </form>
      
      {dashboards.length > 0 && (
        <div className="mt-8 space-y-4">
          <div className="form-group">
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Select Dashboard
            </label>
            <select
              className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
              value={selectedDashboardId}
              onChange={(e) => setSelectedDashboardId(e.target.value)}
            >
              {dashboards.map(dashboard => (
                <option key={dashboard.id} value={dashboard.id}>
                  {dashboard.name} ({dashboard.type})
                </option>
              ))}
            </select>
          </div>
          
          <Button 
            onClick={handleExportDashboard}
            disabled={!selectedDashboardId || loading !== 'idle'}
            isLoading={loading === 'exportingDashboard'}
            variant="primary"
            icon={<ArrowDownToLine className="h-4 w-4" />}
          >
            Export Selected Dashboard
          </Button>
        </div>
      )}
    </div>
  );
};