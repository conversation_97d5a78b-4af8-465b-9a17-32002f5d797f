import React, { useState } from 'react';
import { ExportDashboard } from './ExportDashboard';
import { ImportDashboard } from './ImportDashboard';
import { InfoCard } from './InfoCard';
import { TestHelper } from './TestHelper';
import { CorsHelper } from './CorsHelper';
import { ApiDebugger } from './ApiDebugger';

export const DashboardSharingTool: React.FC = () => {
  const [activeTab, setActiveTab] = useState<'export' | 'import'>('export');

  return (
    <div className="space-y-6">
      <div className="bg-white rounded-lg shadow-md p-6">
        <div className="flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-4 mb-6">
          <button
            onClick={() => setActiveTab('export')}
            className={`px-4 py-2 rounded-md transition-colors ${
              activeTab === 'export'
                ? 'bg-blue-600 text-white'
                : 'bg-gray-100 hover:bg-gray-200'
            }`}
          >
            Export Dashboard
          </button>
          <button
            onClick={() => setActiveTab('import')}
            className={`px-4 py-2 rounded-md transition-colors ${
              activeTab === 'import'
                ? 'bg-blue-600 text-white'
                : 'bg-gray-100 hover:bg-gray-200'
            }`}
          >
            Import Dashboard
          </button>
        </div>

        {activeTab === 'export' ? <ExportDashboard /> : <ImportDashboard />}
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <InfoCard
          title="About Dashboard Sharing"
          content={`
            This tool allows you to export dashboards from one JobTread organization
            and import them into another. It's useful for transferring dashboard designs
            between environments or creating backups.
          `}
        />
        <InfoCard
          title="Limitations"
          content={`
            Dashboards with tiles that reference specific reports, custom fields, or
            data views may not function correctly after import if those entities don't
            exist in the target organization. Role visibility settings are simplified
            in this version.
          `}
          type="warning"
        />
      </div>

      <TestHelper />

      <CorsHelper />

      <ApiDebugger />
    </div>
  );
};