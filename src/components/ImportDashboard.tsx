import React, { useState } from 'react';
import { Upload, ArrowUpFromLine } from 'lucide-react';
import { FormField } from './FormField';
import { importDashboard } from '../services/api';
import { Button } from './Button';
import { useToast } from '../hooks/useToast';

export const ImportDashboard: React.FC = () => {
  const [grantKey, setGrantKey] = useState('');
  const [organizationId, setOrganizationId] = useState('');
  const [dashboardFile, setDashboardFile] = useState<File | null>(null);
  const [newDashboardName, setNewDashboardName] = useState('');
  const [loading, setLoading] = useState(false);
  const [fileData, setFileData] = useState<any>(null);
  const { showToast } = useToast();

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0] || null;
    setDashboardFile(file);
    
    if (file) {
      const reader = new FileReader();
      reader.onload = (event) => {
        try {
          const jsonData = JSON.parse(event.target?.result as string);
          setFileData(jsonData);
          setNewDashboardName(jsonData.name || '');
        } catch (error) {
          showToast('Invalid JSON file. Please select a valid dashboard export.', 'error');
          setDashboardFile(null);
        }
      };
      reader.readAsText(file);
    }
  };

  const handleImportDashboard = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!grantKey || !organizationId || !dashboardFile || !fileData) {
      showToast('Please provide all required information', 'error');
      return;
    }

    setLoading(true);
    try {
      // Prepare the import data
      const importData = {
        ...fileData,
        name: newDashboardName || fileData.name
      };
      
      const result = await importDashboard(grantKey, organizationId, importData);
      
      showToast(`Dashboard "${result.name}" imported successfully!`, 'success');
      
      // Reset form after successful import
      setDashboardFile(null);
      setFileData(null);
      setNewDashboardName('');
      
    } catch (error) {
      console.error('Error importing dashboard:', error);
      showToast('Failed to import dashboard. Please check your data and try again.', 'error');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-xl font-semibold mb-4">Import Dashboard</h2>
        <p className="text-gray-600 mb-4">
          Import a previously exported dashboard into your JobTread organization.
        </p>
      </div>
      
      <form onSubmit={handleImportDashboard} className="space-y-4">
        <FormField
          label="Grant Key"
          type="password"
          value={grantKey}
          onChange={setGrantKey}
          placeholder="Enter your JobTread API Grant Key"
          required
          helpText="The API Grant Key for the target organization"
        />
        
        <FormField
          label="Organization ID"
          type="text"
          value={organizationId}
          onChange={setOrganizationId}
          placeholder="Enter your Organization ID"
          required
          helpText="The ID of the organization where the dashboard will be imported"
        />
        
        <div className="space-y-1">
          <label className="block text-sm font-medium text-gray-700">
            Dashboard File
          </label>
          <div className="mt-1 flex justify-center px-6 pt-5 pb-6 border-2 border-gray-300 border-dashed rounded-md">
            <div className="space-y-1 text-center">
              <Upload className="mx-auto h-12 w-12 text-gray-400" />
              <div className="flex text-sm text-gray-600">
                <label htmlFor="file-upload" className="relative cursor-pointer bg-white rounded-md font-medium text-blue-600 hover:text-blue-500 focus-within:outline-none focus-within:ring-2 focus-within:ring-offset-2 focus-within:ring-blue-500">
                  <span>Upload a dashboard JSON file</span>
                  <input
                    id="file-upload"
                    name="file-upload"
                    type="file"
                    className="sr-only"
                    accept=".json"
                    onChange={handleFileChange}
                  />
                </label>
                <p className="pl-1">or drag and drop</p>
              </div>
              <p className="text-xs text-gray-500">
                JSON files only
              </p>
            </div>
          </div>
          {dashboardFile && (
            <p className="text-sm text-green-600">
              Selected file: {dashboardFile.name}
            </p>
          )}
        </div>
        
        {fileData && (
          <FormField
            label="Dashboard Name"
            type="text"
            value={newDashboardName}
            onChange={setNewDashboardName}
            placeholder="Enter new dashboard name (optional)"
            helpText="Leave blank to use the original name"
          />
        )}
        
        <Button 
          type="submit"
          disabled={!grantKey || !organizationId || !dashboardFile || loading}
          isLoading={loading}
          variant="primary"
          icon={<ArrowUpFromLine className="h-4 w-4" />}
        >
          Import Dashboard
        </Button>
      </form>
    </div>
  );
};