import { Dashboard, DashboardDetails } from '../types/dashboard';

const API_ENDPOINT = 'https://api.jobtread.com/pave';
const API_TIMEOUT = 30000; // 30 seconds timeout

/**
 * Makes a request to the JobTread Pave API
 */
async function makeApiRequest(grantKey: string, query: any) {
  const controller = new AbortController();
  const timeoutId = setTimeout(() => controller.abort(), API_TIMEOUT);

  try {
    if (!navigator.onLine) {
      throw new Error('No internet connection. Please check your network and try again.');
    }

    const response = await fetch(API_ENDPOINT, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        'Origin': window.location.origin,
        'Authorization': `Bearer ${grantKey}`
      },
      credentials: 'include',
      mode: 'cors',
      body: JSON.stringify({ query }),
      signal: controller.signal
    });

    if (!response.ok) {
      if (response.status === 403) {
        throw new Error('Access denied. Please check your grant key and try again.');
      }
      if (response.status === 404) {
        throw new Error('API endpoint not found. Please check the API URL.');
      }
      if (response.status === 0 || response.type === 'opaque') {
        throw new Error('CORS error: Unable to access the API. Please contact support.');
      }
      const errorText = await response.text();
      throw new Error(`API request failed (${response.status}): ${errorText}`);
    }

    const data = await response.json();
    
    if (data.errors) {
      const errorMessage = Array.isArray(data.errors) 
        ? data.errors.map((e: any) => e.message).join(', ')
        : JSON.stringify(data.errors);
      throw new Error(`API returned errors: ${errorMessage}`);
    }
    
    return data.data;
  } catch (error) {
    if (error instanceof Error) {
      if (error.name === 'AbortError') {
        throw new Error('Request timed out. Please try again.');
      }
      if (error.message.includes('Failed to fetch')) {
        throw new Error('Unable to connect to the API. Please check your network connection and try again.');
      }
      throw error;
    }
    throw new Error('An unexpected error occurred');
  } finally {
    clearTimeout(timeoutId);
  }
}

/**
 * Fetches the list of dashboards for an organization
 */
export async function fetchOrganizationDashboards(grantKey: string, organizationId: string): Promise<Dashboard[]> {
  const query = {
    $: { grantKey },
    organization: {
      $: { id: organizationId },
      id: {},
      name: {},
      dashboards: {
        nodes: {
          id: {},
          name: {},
          type: {}
        }
      }
    }
  };

  try {
    const data = await makeApiRequest(grantKey, query);
    return data.organization.dashboards.nodes;
  } catch (error) {
    console.error('Error fetching dashboards:', error);
    throw error;
  }
}

/**
 * Fetches detailed information about a specific dashboard
 */
export async function fetchDashboardDetails(grantKey: string, dashboardId: string): Promise<DashboardDetails> {
  const query = {
    $: { grantKey },
    dashboard: {
      $: { id: dashboardId },
      id: {},
      name: {},
      type: {},
      roles: {
        nodes: {
          id: {}
        }
      },
      tiles: {
        nodes: {
          height: {},
          width: {},
          x: {},
          y: {},
          options: {}
        }
      }
    }
  };

  try {
    const data = await makeApiRequest(grantKey, query);
    return data.dashboard;
  } catch (error) {
    console.error('Error fetching dashboard details:', error);
    throw error;
  }
}

/**
 * Imports a dashboard into the specified organization
 */
export async function importDashboard(grantKey: string, organizationId: string, dashboardData: any) {
  const tiles = dashboardData.tiles.map((tile: any) => ({
    height: tile.height,
    width: tile.width,
    x: tile.x,
    y: tile.y,
    options: tile.options
  }));

  const query = {
    $: { grantKey },
    createDashboard: {
      $: {
        organizationId,
        name: dashboardData.name,
        type: dashboardData.type,
        tiles
      },
      createdDashboard: {
        id: {},
        name: {},
        type: {},
        organization: {
          id: {}
        },
        tiles: {
          nodes: {
            id: {}
          }
        }
      }
    }
  };

  try {
    const data = await makeApiRequest(grantKey, query);
    return data.createDashboard.createdDashboard;
  } catch (error) {
    console.error('Error importing dashboard:', error);
    throw error;
  }
}

/**
 * Fetches the memberships to find organization IDs
 */
export async function fetchOrganizationIds(grantKey: string) {
  const query = {
    $: { grantKey },
    currentGrant: {
      user: {
        memberships: {
          nodes: {
            organization: {
              id: {},
              name: {}
            }
          }
        }
      }
    }
  };

  try {
    const data = await makeApiRequest(grantKey, query);
    return data.currentGrant.user.memberships.nodes.map((node: any) => ({
      id: node.organization.id,
      name: node.organization.name
    }));
  } catch (error) {
    console.error('Error fetching organization IDs:', error);
    throw error;
  }
}